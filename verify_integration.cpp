// 验证集成的简单测试程序
// 编译命令: g++ -I../../common/include -I/usr/include/eigen3 verify_integration.cpp -o verify_integration

#include "src/user/MIT_Controller/Controllers/DisturbanceEst/PostProcessForceCompensator.h"
#include <iostream>

int main() {
    std::cout << "验证PostProcessForceCompensator集成..." << std::endl;
    
    try {
        // 创建补偿器实例
        PostProcessForceCompensator compensator;
        
        // 设置参数
        compensator.setFrictionCoefficient(0.4f);
        compensator.setForceLimit(5.0f, 200.0f);
        compensator.setCompensationWeights(1.0f, 0.8f);
        
        std::cout << "✓ PostProcessForceCompensator创建成功" << std::endl;
        std::cout << "✓ 参数设置成功" << std::endl;
        
        // 测试基本功能
        Vec3<float> original_forces[4];
        Vec3<float> compensated_forces[4];
        Vec3<float> foot_positions[4];
        Vec4<float> contact_states;
        
        // 初始化测试数据
        for(int i = 0; i < 4; i++) {
            original_forces[i] = Vec3<float>(0, 0, 50);  // 50N向上的力
            foot_positions[i] = Vec3<float>(0.2f * (i%2 ? 1 : -1), 0.2f * (i/2 ? 1 : -1), 0);
            contact_states[i] = 1.0f;  // 全部接触
        }
        
        // 测试补偿
        bool success = compensator.compensateForces(
            original_forces,
            contact_states,
            foot_positions,
            2.0f,  // 2kg负载
            Vec3<float>(0, 0, 1),  // 1Nm扰动力矩
            compensated_forces
        );
        
        if(success) {
            std::cout << "✓ 力补偿计算成功" << std::endl;
        } else {
            std::cout << "✗ 力补偿计算失败" << std::endl;
            return 1;
        }
        
        std::cout << "✓ 所有测试通过！集成验证成功。" << std::endl;
        return 0;
        
    } catch(const std::exception& e) {
        std::cout << "✗ 异常: " << e.what() << std::endl;
        return 1;
    } catch(...) {
        std::cout << "✗ 未知异常" << std::endl;
        return 1;
    }
}
