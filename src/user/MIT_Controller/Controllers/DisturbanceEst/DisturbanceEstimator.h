#pragma once

#include "cppTypes.h"                 // Mat3<T> / Vec3<T> 等别名
#include <eigen3/Eigen/Dense>
#include <FSM_States/ControlFSMData.h>
#include <qpOASES.hpp>
#include <vector>

class DisturbanceEstimator {
public:
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
    DisturbanceEstimator(float _dt, int _iterations_between_mpc, MIT_UserParameters* parameters);
    ~DisturbanceEstimator() = default;

    // 生命周期
    void initialize();
    void run(const ControlFSMData<float>& data);
    void update(const ControlFSMData<float>& data);

    // 扭矩->足端力估计（机体系），内部做阻尼伪逆与一阶滤波
    void estimateFootForceFromTorque(const ControlFSMData<float>& data);

    // 简化重力/惯性补偿（可后续替换为RBDL/Pinocchio）
    Vec3<float> computeGravityCompensation(const Vec3<float>& q, int leg, const ControlFSMData<float>& data);
    Vec3<float> computeInertiaCompensation(const Vec3<float>& q, const Vec3<float>& qd, int leg, const ControlFSMData<float>& data);

    // 指令/控制
    void _SetupCommand(const ControlFSMData<float>& data);
    void initializePIDControllers();
    Vec3<float> computeAttitudeError(const Quat<float>& current_quat, const Quat<float>& desired_quat);
    Vec3<float> computeAngularVelocityError(const Vec3<float>& current_omega, const Vec3<float>& desired_omega);
    Vec3<float> computePositionError(const Vec3<float>& current_pos, const Vec3<float>& desired_pos);
    Vec3<float> computeVelocityError(const Vec3<float>& current_vel, const Vec3<float>& desired_vel);
    Vec3<float> attitudePIDControl(const Vec3<float>& attitude_error, const Vec3<float>& angular_velocity_error);
    Vec3<float> positionPIDControl(const Vec3<float>& position_error, const Vec3<float>& velocity_error);
    void updatePIDControllers(const ControlFSMData<float>& data);
    void setPIDGains(const Vec3<float>& att_kp, const Vec3<float>& att_ki, const Vec3<float>& att_kd,
                    const Vec3<float>& pos_kp, const Vec3<float>& pos_ki, const Vec3<float>& pos_kd);
    void printPIDDebugInfo();
    void enablePIDDebug(bool enable);
    void resetPIDIntegrals();

    // 扰动估计（世界系），并缓存相对质心的脚位置向量 r_world 供QP使用
    void updateTauEst(const ControlFSMData<float>& data);

    // 力分配QP（固定规模 + SQProblem热启动）
    void solveQP();

    /* --------- 公有（观测/估计/输出） --------- */
    float mp = 0.0f;                // 力传感器路径估计的负载质量
    float mp_torque_based = 0.0f;   // 扭矩反演路径估计的负载质量
    float mp_fused = 0.0f;          // 融合后的负载质量

    Vec4<float>  contact_state;                 // 连续接触概率/权（0~1）
    Vec4<float>  foot_force_est;                // 足端力传感器估计（通常是Fz）
    Vec3<float>  foot_force_tau_est[4];         // 由关节扭矩反演得到的足端力（机体系三维）
    Vec4<float>  foot_force_fused;              // 融合后（如只在法向用）
    Vec3<float>  F_world_tau_est[4];            // 由关节扭矩反演得到的足端力（世界系三维）
    float        total_contact_force = 0.0f;    // 力传感器总和（标量）
    Vec3<float>  total_contact_force_tau_est;   // 扭矩反演路径力的总和（世界系三维，按实现而定）

    Eigen::Matrix<float,3,3> I_body;            // 机体系转动惯量
    Eigen::Matrix<float,3,3> I_world;           // 世界系转动惯量（通常只取yaw旋转近似）
    float        body_mass = 14.f;              // 机身质量（kg）
    Vec3<float>  acc_world_3d;                  // 世界系线加速度（是否含重力：需在实现中全局统一）
    float        torque_confidence = 1.0f;      // 扭矩路径置信度（融合用）

    Vec3<float>  disturbance;                   // 负载引起的扰动项（语义见实现）
    Vec3<float>  foot_force_des[4];             // QP输出：期望足端反作用力（世界系）
    /**
     * pFoot[i] 的语义已统一为：
     *   世界系下“脚端相对质心”的位置向量 r_world = pFoot_world - pCOM_world
     * 注意：以前若把它当“世界系绝对脚端位置”，需要同步改动！
     */
    Vec3<float>  pFoot[4];

private:
    /* --------- 时序/通用 --------- */
    float dt = 0.f;
    float dtMPC = 0.f;
    int   iterations_between_mpc = 1;
    MIT_UserParameters* _parameters = nullptr;

    Vec3<float> gravity; // (0,0,-9.81)

    /* --------- 传感/判定参数 --------- */
    float mp_tau = 2.f;                  // 质量估计低通常数（s）
    float mp_max = 10.f;                 // 负载上限（kg）
    float last_contact_hys[4] = {0,0,0,0};
    float F_min = 20.f;                  // 接触判定阈值（N）
    float total_mass_original = 0.f;

    bool  enable_torque_estimation   = true;
    bool  enable_gravity_compensation= false;
    bool  enable_inertia_compensation= false;
    float torque_filter_alpha        = 0.7f;  // 0.0(无滤波)~1.0(全历史)
    float fusion_base_weight         = 0.3f;

    /* --------- 扰动估计 --------- */
    Vec3<float> d_est;                  // 扰动状态（语义：实现中以角加速度扰动处理）
    Eigen::Matrix<float,3,3> K;         // 扰动观测/反馈增益（正定）
    Vec3<float> input;                  // 扩展用途
    Vec3<float> x_1, x_2, phi;          // 备用状态
    Vec3<float> F_b;                    // 期望合力（世界系）
    Vec3<float> T_b;                    // 期望力矩（世界系）

    /* --------- PID 控制 --------- */
    // 姿态PID
    Vec3<float> attitude_kp, attitude_ki, attitude_kd;
    Vec3<float> attitude_error_integral, attitude_error_prev;
    Vec3<float> angular_velocity_kp, angular_velocity_kd;
    Vec3<float> angular_velocity_error_prev;
    // 位置PID
    Vec3<float> position_kp, position_ki, position_kd;
    Vec3<float> position_error_integral, position_error_prev;
    Vec3<float> velocity_kp, velocity_kd;
    Vec3<float> velocity_error_prev;
    // 输出
    Vec3<float> attitude_control_torque;   // 世界系
    Vec3<float> position_control_force;    // 世界系
    // 使能与限幅
    bool  enable_attitude_pid = true;
    bool  enable_position_pid = true;
    float attitude_integral_limit = 10.f;
    float position_integral_limit = 50.f;
    // 调试
    bool  enable_pid_debug = true;
    int   debug_counter = 0;
    int   debug_print_frequency = 100;

    /* --------- 期望轨迹/命令 --------- */
    float _body_height = 0.f;
    float _yaw_turn_rate = 0.f;
    float _x_vel_des = 0.f;
    float _y_vel_des = 0.f;
    float _yaw_des = 0.f;
    float _roll_des = 0.f;
    float _pitch_des = 0.f;

    /* --------- QP 参数（固定规模 + 热启动） --------- */
    // 维度常量（固定规模：4条腿 × 每腿3分量 = 12变量；每腿5条摩擦锥=20约束）
    static constexpr int    kDimPerLeg   = 3;
    static constexpr int    kLegs        = 4;
    static constexpr int    kNV          = kLegs * kDimPerLeg;   // 12
    static constexpr int    kIneqPerLeg  = 5;
    static constexpr int    kNC          = kLegs * kIneqPerLeg;  // 20

    // 摩擦、边界
    float   mu    = 0.4f;     // 摩擦系数
    float   f_max = 500.f;    // 单脚力上界

    // 数值稳健性与热启动控制（类内常量，cpp中直接使用）
    static constexpr double kHugeBound  = 1e6;    // 有限大界，替代 ±inf
    static constexpr double kCpuTimeMax = 0.004;  // qpOASES cpu时间上限(s)
    static constexpr int    kWSRMax     = 2000;   // qpOASES 迭代上限
    static constexpr double kQEqWeight  = 10.0;   // 软等式权重（Q）
    static constexpr double kRDiag      = 1e-4;   // 力正则（R对角）
    static constexpr double kFzMinBound = 0.0;    // Fz下界
    static constexpr float  kJtDamp     = 1e-4f;  // (J^T)阻尼伪逆的阻尼

    // qpOASES 热启动状态（用成员替代函数内static，便于管理）
    bool                 qp_initialized_ = false;
    // SQProblem 需要在 initialize() 中以固定规模构造（12, 20）
    std::unique_ptr<qpOASES::SQProblem> qp_;
};
