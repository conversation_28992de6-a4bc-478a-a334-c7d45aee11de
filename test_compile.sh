#!/bin/bash

# 测试编译脚本
echo "开始测试编译..."

# 进入工作目录
cd /home/<USER>/Desktop/03_mitcontrol_latest_real

# 清理之前的编译
echo "清理之前的编译..."
catkin_make clean

# 重新编译
echo "开始编译..."
catkin_make -j4

# 检查编译结果
if [ $? -eq 0 ]; then
    echo "编译成功！"
    echo "检查链接产物..."
    if [ -f "devel/lib/mit_ctrl/mit_ctrl" ]; then
        echo "链接产物生成成功！"
        exit 0
    else
        echo "链接产物未找到！"
        exit 1
    fi
else
    echo "编译失败！"
    exit 1
fi
